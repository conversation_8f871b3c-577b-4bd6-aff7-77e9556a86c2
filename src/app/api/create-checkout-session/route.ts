// /api/create-checkout-session
import Stripe from 'stripe'
import { getPriceId } from '@/lib/stripe-plans';
import { getOrCreateStripeCustomer } from '@/lib/stripe-customer';
import { createClient } from '@supabase/supabase-js';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export async function POST(request: Request) {
  try {
    const { planId, isYearly, siteId, autoRenewal = true } = await request.json()

    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return new Response(JSON.stringify({ error: 'Not authenticated' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user) {
      return new Response(JSON.stringify({ error: 'Invalid user' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get or create Stripe customer for this user
    const stripeCustomerId = await getOrCreateStripeCustomer(user.id, user.email!);

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    const session = await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      customer: stripeCustomerId, // Use the user's Stripe customer ID
      line_items: [
        {
          price: getPriceId(planId, isYearly), // Your price IDs
          quantity: 1,
        },
      ],
      subscription_data: {
        metadata: {
          planId,
          isYearly: String(isYearly),
          siteId: siteId || '',
          userId: user.id,
          autoRenewal: String(autoRenewal),
        },
        // Set cancel_at_period_end based on auto-renewal setting
        cancel_at_period_end: !autoRenewal,
      },
      metadata: {
        planId,
        isYearly: String(isYearly),
        siteId: siteId || '',
        userId: user.id, // Add user ID to metadata for webhook processing
        autoRenewal: String(autoRenewal),
      },
      success_url: `${baseUrl}/dashboard?siteId=${siteId || ''}&postCheckout=1`,
      cancel_url: `${baseUrl}/payments?siteId=${siteId || ''}`,

    })

    return Response.json({ url: session.url })
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return new Response(JSON.stringify({ error: 'Failed to create checkout session' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}