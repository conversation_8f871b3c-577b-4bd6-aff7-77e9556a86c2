-- Create subscriptions table to track subscription status and auto-renewal settings
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    stripe_subscription_id VARCHAR(255) UNIQUE NOT NULL,
    stripe_customer_id VARCHAR(255) NOT NULL,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    site_id UUID REFERENCES "user-websites"(id) ON DELETE SET NULL,
    plan_id VARCHAR(50),
    status VARCHAR(50) NOT NULL CHECK (status IN ('active', 'past_due', 'unpaid', 'cancelled', 'incomplete', 'incomplete_expired', 'trialing')),
    current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    auto_renewal BOOLEAN NOT NULL DEFAULT true,
    amount INTEGER NOT NULL DEFAULT 0, -- Amount in cents
    currency VARCHAR(3) NOT NULL DEFAULT 'usd',
    interval VARCHAR(20) NOT NULL DEFAULT 'month' CHECK (interval IN ('day', 'week', 'month', 'year')),
    cancelled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_subscription_id ON subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer_id ON subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_site_id ON subscriptions(site_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_auto_renewal ON subscriptions(auto_renewal);

-- Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_subscriptions_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_subscriptions_updated_at 
    BEFORE UPDATE ON subscriptions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_subscriptions_updated_at_column();

-- Add RLS (Row Level Security) policies
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to see only their own subscriptions
CREATE POLICY "Users can view their own subscriptions" ON subscriptions
    FOR SELECT USING (user_id = auth.uid());

-- Policy to allow users to update their own subscriptions (for auto-renewal toggle)
CREATE POLICY "Users can update their own subscriptions" ON subscriptions
    FOR UPDATE USING (user_id = auth.uid());

-- Policy to allow service role to insert/update/delete subscriptions (for webhooks)
CREATE POLICY "Service role can manage all subscriptions" ON subscriptions
    FOR ALL USING (auth.role() = 'service_role');

-- Add comments to document the table
COMMENT ON TABLE subscriptions IS 'Tracks user subscription status and auto-renewal settings';
COMMENT ON COLUMN subscriptions.stripe_subscription_id IS 'Unique Stripe subscription ID';
COMMENT ON COLUMN subscriptions.stripe_customer_id IS 'Stripe customer ID associated with the subscription';
COMMENT ON COLUMN subscriptions.user_id IS 'User who owns the subscription';
COMMENT ON COLUMN subscriptions.site_id IS 'Website associated with the subscription (nullable)';
COMMENT ON COLUMN subscriptions.plan_id IS 'Plan identifier (starter, pro, turbo)';
COMMENT ON COLUMN subscriptions.status IS 'Current subscription status from Stripe';
COMMENT ON COLUMN subscriptions.auto_renewal IS 'Whether the subscription should auto-renew';
COMMENT ON COLUMN subscriptions.amount IS 'Subscription amount in cents';
COMMENT ON COLUMN subscriptions.currency IS 'Currency code (USD, AUD, etc.)';
COMMENT ON COLUMN subscriptions.interval IS 'Billing interval (month, year)';
COMMENT ON COLUMN subscriptions.cancelled_at IS 'When the subscription was cancelled (if applicable)';
